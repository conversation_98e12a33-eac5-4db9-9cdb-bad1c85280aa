using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(Tasks))]public class TasksEditor : Editor
{

    public override void OnInspectorGUI()
    {


        Tasks task = (Tasks)target;
        GUILayout.BeginHorizontal();
        GUILayout.Label($"Type:", GUILayout.Width(35));
        TaskType newTaskType = (TaskType)EditorGUILayout.EnumPopup(task.taskType, GUILayout.Width(80));

        if (newTaskType != task.taskType)
        {
            Undo.RecordObject(task, "Change Task Type");
            task.taskType = newTaskType;
            EditorUtility.SetDirty(task);
        }
        GUILayout.EndHorizontal();
        GUILayout.Space(10);
        BuildingManager buildingManager = FindFirstObjectByType<BuildingManager>();
        string[] buildingNames = new string[0];
        if (buildingManager != null)
        {
            var buildingDataList = buildingManager.GetAllBuildingData();
            buildingNames = new string[buildingDataList.Count];
            for (int i = 1; i < buildingDataList.Count; i++)
            {
                buildingNames[i] = buildingDataList[i].buildingName;
            }
        }

        GUILayout.BeginHorizontal();
        GUILayout.Label("Building:", GUILayout.Width(60));
        int currentBuildingIndex = Mathf.Clamp(task.buildingIndex, 1, buildingNames.Length - 1);
        int newBuildingIndex = EditorGUILayout.Popup(currentBuildingIndex, buildingNames, GUILayout.Width(120));

        if (newBuildingIndex != task.buildingIndex)
        {
            Undo.RecordObject(task, "Change Task Building");
            task.buildingIndex = newBuildingIndex;
            EditorUtility.SetDirty(task);
        }
        GUILayout.EndHorizontal();



    }
}