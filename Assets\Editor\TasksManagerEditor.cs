using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(TasksManager))]public class TasksManagerEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        TasksManager tasksManager = (TasksManager)target;

        if (GUILayout.Button("Reload Tasks"))
        {
            tasksManager.LoadDataFromChildren();
        }

        if (GUILayout.Button("Reset Task Done"))
        {
            Tasks.taskCount = 0;
        }

        GUILayout.Label("Task Done: " + Tasks.taskCount);

        // Display tasks in a tab-like interface
        if (tasksManager.tasks != null && tasksManager.tasks.Length > 0)
        {
            GUILayout.Space(10);
            GUILayout.Label("Tasks:", EditorStyles.boldLabel);

            // Get building manager for dropdown options
            BuildingManager buildingManager = FindFirstObjectByType<BuildingManager>();
            string[] buildingNames = new string[0];

            if (buildingManager != null)
            {
                var buildingDataList = buildingManager.GetAllBuildingData();
                buildingNames = new string[buildingDataList.Count];
                for (int i = 0; i < buildingDataList.Count; i++)
                {
                    buildingNames[i] = buildingDataList[i].buildingName;
                }
            }

            // Display each task in a tab-like format
            for (int i = 0; i < tasksManager.tasks.Length; i++)
            {
                Tasks task = tasksManager.tasks[i];
                if (task == null) continue;

                // Create a box for each task (tab-like appearance)
                EditorGUILayout.BeginVertical("box");

                // Task header with name
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label($"Task {i + 1}: {task.name}", EditorStyles.boldLabel);
                GUILayout.FlexibleSpace();
                GUILayout.Label($"Type:", GUILayout.Width(35));
                TaskType newTaskType = (TaskType)EditorGUILayout.EnumPopup(task.taskType, GUILayout.Width(80));

                if (newTaskType != task.taskType)
                {
                    Undo.RecordObject(task, "Change Task Type");
                    task.taskType = newTaskType;
                    EditorUtility.SetDirty(task);
                }
                EditorGUILayout.EndHorizontal();

                // Task controls row
                EditorGUILayout.BeginHorizontal();

                // Building selection dropdown
                if (buildingNames.Length > 0)
                {
                    GUILayout.Label("Building:", GUILayout.Width(60));
                    int currentBuildingIndex = Mathf.Clamp(task.buildingIndex, 0, buildingNames.Length - 1);
                    int newBuildingIndex = EditorGUILayout.Popup(currentBuildingIndex, buildingNames, GUILayout.Width(120));

                    if (newBuildingIndex != task.buildingIndex)
                    {
                        Undo.RecordObject(task, "Change Task Building");
                        task.buildingIndex = newBuildingIndex;
                        EditorUtility.SetDirty(task);
                    }
                }
                else
                {
                    GUILayout.Label("No buildings found", GUILayout.Width(120));
                }

                GUILayout.FlexibleSpace();

                // Task action buttons
                if (GUILayout.Button("Complete", GUILayout.Width(70)))
                {
                    if (EditorUtility.DisplayDialog("Complete Task",
                        $"Are you sure you want to complete task '{task.name}'?",
                        "Yes", "No"))
                    {
                        task.CompleteTask();
                        tasksManager.LoadDataFromChildren(); // Refresh the task list
                    }
                }

                if (GUILayout.Button("Delete", GUILayout.Width(60)))
                {
                    if (EditorUtility.DisplayDialog("Delete Task",
                        $"Are you sure you want to delete task '{task.name}'?",
                        "Yes", "No"))
                    {
                        Undo.DestroyObjectImmediate(task.gameObject);
                        tasksManager.LoadDataFromChildren(); // Refresh the task list
                    }
                }

                EditorGUILayout.EndHorizontal();

                // Additional task info
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label($"Building Index: {task.buildingIndex}", EditorStyles.miniLabel);
                GUILayout.FlexibleSpace();
                GUILayout.Label($"Position: {task.transform.position}", EditorStyles.miniLabel);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.EndVertical();
                GUILayout.Space(5);
            }
        }
        else
        {
            GUILayout.Space(10);
            EditorGUILayout.HelpBox("No tasks found. Make sure tasks are children of this TasksManager.", MessageType.Info);
        }
    }
}