{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 36960, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 36960, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 36960, "tid": 108880, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 36960, "tid": 108880, "ts": 1754499814292325, "dur": 1631, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 36960, "tid": 108880, "ts": 1754499814300093, "dur": 922, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 36960, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 36960, "tid": 1, "ts": 1754499813004867, "dur": 13999, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 36960, "tid": 1, "ts": 1754499813018871, "dur": 96321, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 36960, "tid": 1, "ts": 1754499813115204, "dur": 54539, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 36960, "tid": 108880, "ts": 1754499814301019, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 36960, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499812996001, "dur": 307, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499812996310, "dur": 1280972, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499812997085, "dur": 4203, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813001293, "dur": 1403, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813002698, "dur": 654, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813003356, "dur": 12, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813003369, "dur": 311, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813003684, "dur": 6, "ph": "X", "name": "ProcessMessages 8781", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813003720, "dur": 196, "ph": "X", "name": "ReadAsync 8781", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813003965, "dur": 4, "ph": "X", "name": "ProcessMessages 5159", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813003971, "dur": 772, "ph": "X", "name": "ReadAsync 5159", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813004747, "dur": 4, "ph": "X", "name": "ProcessMessages 4290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813004751, "dur": 720, "ph": "X", "name": "ReadAsync 4290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005474, "dur": 9, "ph": "X", "name": "ProcessMessages 15058", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005484, "dur": 35, "ph": "X", "name": "ReadAsync 15058", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005522, "dur": 96, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005630, "dur": 170, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005805, "dur": 1, "ph": "X", "name": "ProcessMessages 2261", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005807, "dur": 29, "ph": "X", "name": "ReadAsync 2261", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005839, "dur": 40, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005882, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005885, "dur": 100, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005990, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813005994, "dur": 44, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813006041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813006043, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813006167, "dur": 5, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813006175, "dur": 752, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813006933, "dur": 7, "ph": "X", "name": "ProcessMessages 1633", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813006943, "dur": 467, "ph": "X", "name": "ReadAsync 1633", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007414, "dur": 2, "ph": "X", "name": "ProcessMessages 1149", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007417, "dur": 144, "ph": "X", "name": "ReadAsync 1149", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007587, "dur": 2, "ph": "X", "name": "ProcessMessages 1940", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007590, "dur": 57, "ph": "X", "name": "ReadAsync 1940", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007675, "dur": 2, "ph": "X", "name": "ProcessMessages 2836", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007678, "dur": 76, "ph": "X", "name": "ReadAsync 2836", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007758, "dur": 1, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007760, "dur": 30, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007790, "dur": 1, "ph": "X", "name": "ProcessMessages 1962", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813007793, "dur": 212, "ph": "X", "name": "ReadAsync 1962", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813008029, "dur": 159, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813008437, "dur": 39, "ph": "X", "name": "ProcessMessages 1502", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813008525, "dur": 311, "ph": "X", "name": "ReadAsync 1502", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813008838, "dur": 7, "ph": "X", "name": "ProcessMessages 12617", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813008846, "dur": 299, "ph": "X", "name": "ReadAsync 12617", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813009175, "dur": 2, "ph": "X", "name": "ProcessMessages 2253", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813009304, "dur": 150, "ph": "X", "name": "ReadAsync 2253", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813009458, "dur": 6, "ph": "X", "name": "ProcessMessages 7569", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813009508, "dur": 129, "ph": "X", "name": "ReadAsync 7569", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813009768, "dur": 2, "ph": "X", "name": "ProcessMessages 1626", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813009773, "dur": 94, "ph": "X", "name": "ReadAsync 1626", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813009869, "dur": 4, "ph": "X", "name": "ProcessMessages 5453", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813009906, "dur": 80, "ph": "X", "name": "ReadAsync 5453", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813010114, "dur": 36, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813010193, "dur": 190, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813010500, "dur": 5, "ph": "X", "name": "ProcessMessages 7029", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813010541, "dur": 208, "ph": "X", "name": "ReadAsync 7029", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813010753, "dur": 3, "ph": "X", "name": "ProcessMessages 2584", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813011958, "dur": 283, "ph": "X", "name": "ReadAsync 2584", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012281, "dur": 15, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012298, "dur": 49, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012392, "dur": 3, "ph": "X", "name": "ProcessMessages 1105", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012510, "dur": 64, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012618, "dur": 4, "ph": "X", "name": "ProcessMessages 4086", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012624, "dur": 91, "ph": "X", "name": "ReadAsync 4086", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012743, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012746, "dur": 92, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012839, "dur": 1, "ph": "X", "name": "ProcessMessages 1850", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813012888, "dur": 115, "ph": "X", "name": "ReadAsync 1850", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013004, "dur": 2, "ph": "X", "name": "ProcessMessages 2450", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013048, "dur": 65, "ph": "X", "name": "ReadAsync 2450", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013154, "dur": 2, "ph": "X", "name": "ProcessMessages 3471", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013228, "dur": 149, "ph": "X", "name": "ReadAsync 3471", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013378, "dur": 3, "ph": "X", "name": "ProcessMessages 5816", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013419, "dur": 96, "ph": "X", "name": "ReadAsync 5816", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013516, "dur": 1, "ph": "X", "name": "ProcessMessages 1404", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013518, "dur": 62, "ph": "X", "name": "ReadAsync 1404", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013622, "dur": 1, "ph": "X", "name": "ProcessMessages 1915", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013624, "dur": 27, "ph": "X", "name": "ReadAsync 1915", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013733, "dur": 1, "ph": "X", "name": "ProcessMessages 2193", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013735, "dur": 147, "ph": "X", "name": "ReadAsync 2193", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813013944, "dur": 58, "ph": "X", "name": "ProcessMessages 2477", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014045, "dur": 95, "ph": "X", "name": "ReadAsync 2477", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014143, "dur": 6, "ph": "X", "name": "ProcessMessages 7260", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014150, "dur": 141, "ph": "X", "name": "ReadAsync 7260", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014294, "dur": 1, "ph": "X", "name": "ProcessMessages 1478", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014332, "dur": 107, "ph": "X", "name": "ReadAsync 1478", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014440, "dur": 1, "ph": "X", "name": "ProcessMessages 2719", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014442, "dur": 73, "ph": "X", "name": "ReadAsync 2719", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014518, "dur": 2, "ph": "X", "name": "ProcessMessages 2447", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014595, "dur": 104, "ph": "X", "name": "ReadAsync 2447", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014702, "dur": 3, "ph": "X", "name": "ProcessMessages 4913", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014706, "dur": 24, "ph": "X", "name": "ReadAsync 4913", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014761, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813014762, "dur": 242, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813015006, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813015008, "dur": 138, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813015149, "dur": 39, "ph": "X", "name": "ProcessMessages 7439", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813015190, "dur": 109, "ph": "X", "name": "ReadAsync 7439", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813015332, "dur": 1, "ph": "X", "name": "ProcessMessages 1138", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813015334, "dur": 722, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016058, "dur": 1, "ph": "X", "name": "ProcessMessages 1336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016060, "dur": 16, "ph": "X", "name": "ReadAsync 1336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016079, "dur": 88, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016169, "dur": 13, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016185, "dur": 59, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016246, "dur": 64, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016311, "dur": 1, "ph": "X", "name": "ProcessMessages 1394", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016313, "dur": 65, "ph": "X", "name": "ReadAsync 1394", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016415, "dur": 155, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016573, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016575, "dur": 53, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016631, "dur": 2, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016634, "dur": 45, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016682, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016685, "dur": 38, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016727, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016730, "dur": 27, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016760, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016762, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016804, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016835, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813016838, "dur": 165, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017006, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017008, "dur": 44, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017100, "dur": 2, "ph": "X", "name": "ProcessMessages 1505", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017103, "dur": 33, "ph": "X", "name": "ReadAsync 1505", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017139, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017142, "dur": 60, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017206, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017263, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017288, "dur": 16, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017306, "dur": 53, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017364, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017390, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017393, "dur": 105, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017501, "dur": 2, "ph": "X", "name": "ProcessMessages 1639", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017504, "dur": 46, "ph": "X", "name": "ReadAsync 1639", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017580, "dur": 57, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017684, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017801, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017803, "dur": 46, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017850, "dur": 1, "ph": "X", "name": "ProcessMessages 1466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017852, "dur": 79, "ph": "X", "name": "ReadAsync 1466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813017978, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018091, "dur": 2, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018094, "dur": 42, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018140, "dur": 1, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018142, "dur": 28, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018172, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018175, "dur": 75, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018253, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018256, "dur": 24, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018281, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018283, "dur": 26, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018312, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018362, "dur": 91, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018500, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018502, "dur": 27, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018575, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018578, "dur": 52, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018632, "dur": 2, "ph": "X", "name": "ProcessMessages 2422", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018636, "dur": 31, "ph": "X", "name": "ReadAsync 2422", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018670, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018672, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018698, "dur": 78, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018780, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018874, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018876, "dur": 80, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018957, "dur": 1, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018959, "dur": 21, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813018982, "dur": 24, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019008, "dur": 17, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019027, "dur": 75, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019176, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019212, "dur": 1, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019214, "dur": 23, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019240, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019262, "dur": 99, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019363, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019386, "dur": 74, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019462, "dur": 18, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019482, "dur": 82, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019623, "dur": 1, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019626, "dur": 89, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019717, "dur": 1, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019720, "dur": 40, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019763, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019766, "dur": 31, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019801, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019876, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019912, "dur": 24, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019938, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813019940, "dur": 23, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020007, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020072, "dur": 22, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020096, "dur": 14, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020112, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020181, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020330, "dur": 2, "ph": "X", "name": "ProcessMessages 1065", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020333, "dur": 33, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020368, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020371, "dur": 47, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020420, "dur": 36, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020459, "dur": 1, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020461, "dur": 13, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020514, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020540, "dur": 82, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020625, "dur": 14, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020642, "dur": 130, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020775, "dur": 1, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020778, "dur": 23, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020803, "dur": 1, "ph": "X", "name": "ProcessMessages 1206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813020805, "dur": 201, "ph": "X", "name": "ReadAsync 1206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021010, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021012, "dur": 25, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021039, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021041, "dur": 12, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021106, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021109, "dur": 24, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021136, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021139, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021160, "dur": 87, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021249, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021251, "dur": 27, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021283, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021303, "dur": 24, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021331, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021334, "dur": 16, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021353, "dur": 50, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021407, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021486, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021489, "dur": 25, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021518, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021549, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021551, "dur": 67, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021620, "dur": 13, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021635, "dur": 68, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021708, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021747, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021749, "dur": 29, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021780, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021782, "dur": 77, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021862, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021864, "dur": 29, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021898, "dur": 25, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021925, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813021927, "dur": 18, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813022021, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813022023, "dur": 36, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813022062, "dur": 2, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813023675, "dur": 180, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813023858, "dur": 10, "ph": "X", "name": "ProcessMessages 15569", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813023869, "dur": 17, "ph": "X", "name": "ReadAsync 15569", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813023888, "dur": 79, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813023969, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024073, "dur": 1, "ph": "X", "name": "ProcessMessages 1657", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024075, "dur": 25, "ph": "X", "name": "ReadAsync 1657", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024102, "dur": 57, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024193, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024209, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024227, "dur": 63, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024292, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024293, "dur": 47, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024343, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024365, "dur": 14, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024381, "dur": 13, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024396, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024453, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024533, "dur": 23, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024556, "dur": 1, "ph": "X", "name": "ProcessMessages 1307", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024558, "dur": 50, "ph": "X", "name": "ReadAsync 1307", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024610, "dur": 50, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024711, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024778, "dur": 1, "ph": "X", "name": "ProcessMessages 2411", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024780, "dur": 61, "ph": "X", "name": "ReadAsync 2411", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024842, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024844, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024900, "dur": 30, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813024932, "dur": 74, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025009, "dur": 26, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025037, "dur": 19, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025058, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025150, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025179, "dur": 1, "ph": "X", "name": "ProcessMessages 1639", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025181, "dur": 78, "ph": "X", "name": "ReadAsync 1639", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025261, "dur": 67, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025332, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025334, "dur": 15, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025388, "dur": 89, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025510, "dur": 1, "ph": "X", "name": "ProcessMessages 1201", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025512, "dur": 20, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025533, "dur": 1, "ph": "X", "name": "ProcessMessages 1179", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025534, "dur": 41, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025579, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025598, "dur": 14, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025615, "dur": 43, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025660, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025699, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025721, "dur": 60, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025782, "dur": 1, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025784, "dur": 34, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025852, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025854, "dur": 79, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025978, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813025980, "dur": 35, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026016, "dur": 1, "ph": "X", "name": "ProcessMessages 2093", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026018, "dur": 14, "ph": "X", "name": "ReadAsync 2093", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026034, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026141, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026184, "dur": 1, "ph": "X", "name": "ProcessMessages 1796", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026186, "dur": 16, "ph": "X", "name": "ReadAsync 1796", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026204, "dur": 13, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026219, "dur": 115, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026336, "dur": 77, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026414, "dur": 1, "ph": "X", "name": "ProcessMessages 1136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026416, "dur": 58, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026476, "dur": 124, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026602, "dur": 1, "ph": "X", "name": "ProcessMessages 2479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026604, "dur": 20, "ph": "X", "name": "ReadAsync 2479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026668, "dur": 16, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026723, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026725, "dur": 79, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026805, "dur": 1, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813026806, "dur": 322, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027132, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027134, "dur": 40, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027176, "dur": 2, "ph": "X", "name": "ProcessMessages 2089", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027179, "dur": 116, "ph": "X", "name": "ReadAsync 2089", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027298, "dur": 33, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027335, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027339, "dur": 62, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027403, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027405, "dur": 107, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027514, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027517, "dur": 72, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027590, "dur": 1, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027591, "dur": 17, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027656, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027764, "dur": 1, "ph": "X", "name": "ProcessMessages 1129", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027766, "dur": 20, "ph": "X", "name": "ReadAsync 1129", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027828, "dur": 1, "ph": "X", "name": "ProcessMessages 1128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027830, "dur": 46, "ph": "X", "name": "ReadAsync 1128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027878, "dur": 66, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027946, "dur": 1, "ph": "X", "name": "ProcessMessages 2381", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813027948, "dur": 50, "ph": "X", "name": "ReadAsync 2381", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028027, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028043, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028158, "dur": 1, "ph": "X", "name": "ProcessMessages 2108", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028160, "dur": 39, "ph": "X", "name": "ReadAsync 2108", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028201, "dur": 51, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028254, "dur": 48, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028303, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028387, "dur": 48, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028436, "dur": 32, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028470, "dur": 52, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028545, "dur": 1, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028546, "dur": 17, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028608, "dur": 16, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028651, "dur": 39, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028714, "dur": 99, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028818, "dur": 2, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028821, "dur": 58, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028935, "dur": 3, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813028939, "dur": 136, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029077, "dur": 1, "ph": "X", "name": "ProcessMessages 1206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029080, "dur": 25, "ph": "X", "name": "ReadAsync 1206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029108, "dur": 22, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029169, "dur": 72, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029243, "dur": 21, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029291, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029354, "dur": 19, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029376, "dur": 43, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029499, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029656, "dur": 1, "ph": "X", "name": "ProcessMessages 2212", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029658, "dur": 41, "ph": "X", "name": "ReadAsync 2212", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029701, "dur": 52, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029828, "dur": 1, "ph": "X", "name": "ProcessMessages 1036", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029829, "dur": 24, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029895, "dur": 1, "ph": "X", "name": "ProcessMessages 1092", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029897, "dur": 93, "ph": "X", "name": "ReadAsync 1092", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813029992, "dur": 13, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030049, "dur": 14, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030065, "dur": 73, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030176, "dur": 70, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030248, "dur": 128, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030378, "dur": 1, "ph": "X", "name": "ProcessMessages 2205", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030380, "dur": 62, "ph": "X", "name": "ReadAsync 2205", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030444, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030516, "dur": 1, "ph": "X", "name": "ProcessMessages 1795", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030518, "dur": 51, "ph": "X", "name": "ReadAsync 1795", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030571, "dur": 147, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030762, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030803, "dur": 57, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030862, "dur": 1, "ph": "X", "name": "ProcessMessages 1850", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030864, "dur": 20, "ph": "X", "name": "ReadAsync 1850", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813030919, "dur": 48, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813031027, "dur": 115, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813031218, "dur": 2, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813031222, "dur": 74, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813031342, "dur": 3, "ph": "X", "name": "ProcessMessages 1954", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813031347, "dur": 94, "ph": "X", "name": "ReadAsync 1954", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813031445, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813031447, "dur": 133, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813031643, "dur": 382, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032029, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032108, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032167, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032252, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032255, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032350, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032352, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032419, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032422, "dur": 174, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032600, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032603, "dur": 26, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032672, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032676, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032802, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032805, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032898, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813032931, "dur": 35, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033068, "dur": 43, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033153, "dur": 94, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033250, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033253, "dur": 105, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033363, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033366, "dur": 51, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033420, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033423, "dur": 26, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033452, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033454, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033515, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033517, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033645, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033647, "dur": 104, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033800, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033803, "dur": 33, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033889, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033891, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033970, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813033973, "dur": 31, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034071, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034075, "dur": 84, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034162, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034201, "dur": 52, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034289, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034292, "dur": 61, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034369, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034373, "dur": 129, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034574, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034578, "dur": 32, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034612, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034616, "dur": 65, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034715, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034719, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034786, "dur": 56, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813034843, "dur": 279, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035126, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035130, "dur": 51, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035205, "dur": 4, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035210, "dur": 101, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035316, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035352, "dur": 77, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035453, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035489, "dur": 142, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035634, "dur": 5, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035723, "dur": 38, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035764, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035768, "dur": 63, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035904, "dur": 39, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813035946, "dur": 68, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036017, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036021, "dur": 32, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036056, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036059, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036090, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036093, "dur": 136, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036232, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036234, "dur": 65, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036346, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036349, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036463, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036466, "dur": 107, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036577, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036581, "dur": 52, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036641, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036644, "dur": 31, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036697, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036726, "dur": 60, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036789, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036791, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036816, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813036818, "dur": 210, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037043, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037045, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037077, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037080, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037135, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037137, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037298, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037301, "dur": 83, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037408, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037411, "dur": 67, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037543, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037545, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037608, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037611, "dur": 126, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037770, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037773, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037859, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037863, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037891, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037894, "dur": 20, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037918, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813037964, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813038035, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813038037, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813038104, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813038172, "dur": 1055, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813039235, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813039241, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813039308, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813039310, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813039478, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813039555, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813039597, "dur": 468, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040084, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040086, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040174, "dur": 74, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040251, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040346, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040348, "dur": 257, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040610, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040710, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813040713, "dur": 10709, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813051527, "dur": 45, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813051613, "dur": 180, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813051807, "dur": 10, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813051828, "dur": 28790, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813080627, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813080632, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813080696, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813080700, "dur": 9773, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090483, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090487, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090545, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090548, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090583, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090683, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090686, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090706, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813090709, "dur": 348, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091062, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091101, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091106, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091207, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091236, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091239, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091380, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091418, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091421, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091458, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091462, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091495, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091497, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091585, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091614, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091616, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091648, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091650, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091692, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091743, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091745, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091781, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091784, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091817, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091819, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091849, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091878, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091961, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091963, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091996, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813091998, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092040, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092042, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092073, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092142, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092178, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092180, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092208, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092238, "dur": 305, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092549, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092576, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092578, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092605, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092607, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092634, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092775, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092824, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092827, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092862, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092865, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092896, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092898, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092969, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813092997, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093000, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093063, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093096, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093129, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093168, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093173, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093208, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093210, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093238, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093241, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093277, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093279, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093311, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093314, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093426, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093456, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093575, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093607, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093609, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093636, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093639, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093689, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093716, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093719, "dur": 112, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093836, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093838, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093874, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813093877, "dur": 147, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094030, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094061, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094063, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094092, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094094, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094134, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094138, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094180, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094184, "dur": 35, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094225, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094349, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094380, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094439, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094468, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094470, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094496, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094498, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094528, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094530, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094557, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094560, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094588, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094590, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094639, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094679, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094681, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094714, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094716, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094745, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094977, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813094979, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095011, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095013, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095042, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095068, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095070, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095125, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095150, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095151, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095177, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095179, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095275, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095315, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095318, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095378, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095382, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095416, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095418, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095447, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095451, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095478, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095480, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095507, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095509, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095558, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095585, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095665, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095695, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095739, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095767, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095769, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095819, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095821, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095861, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095864, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095947, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095984, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813095987, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096019, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096022, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096075, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096079, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096120, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096123, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096154, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096181, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096183, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096268, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096270, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096303, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096305, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096337, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096340, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096461, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096463, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096495, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096498, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096548, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096579, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096581, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096725, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096728, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096756, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096758, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096824, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096858, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096861, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096935, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096966, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813096968, "dur": 148, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097123, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097156, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097159, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097188, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097190, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097266, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097296, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097298, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097327, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097439, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097442, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097472, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097474, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097526, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097556, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097560, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097595, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097597, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097630, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097632, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097659, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097663, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097772, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097817, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097873, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097903, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097906, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813097935, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098052, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098083, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098085, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098162, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098164, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098219, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098223, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098257, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098259, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098286, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098289, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098390, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098457, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098460, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098495, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098498, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098585, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098615, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098616, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098644, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098647, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098685, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098716, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098719, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813098751, "dur": 862, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099618, "dur": 62, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099685, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099689, "dur": 34, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099727, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099728, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099761, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099763, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099795, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099797, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099825, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099827, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099860, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813099862, "dur": 150, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100020, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100065, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100068, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100119, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100122, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100171, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100173, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100284, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100286, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100316, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100318, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100345, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100387, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100416, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100419, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100448, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100451, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100479, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100481, "dur": 56, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100541, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100543, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100577, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100579, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100613, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100616, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100651, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100654, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100768, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100809, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100812, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100855, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100857, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100895, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100897, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100938, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100941, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813100978, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101003, "dur": 217, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101224, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101226, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101294, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101297, "dur": 183, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101485, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101488, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101617, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101619, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101700, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101750, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101754, "dur": 147, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813101908, "dur": 173, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102085, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102088, "dur": 427, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102520, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102523, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102561, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102564, "dur": 153, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102724, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102856, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102860, "dur": 118, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102981, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813102984, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103115, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103117, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103239, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103241, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103348, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103350, "dur": 118, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103473, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103475, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103519, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103521, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103568, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103572, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103607, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103610, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103648, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103650, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103686, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103689, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103771, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103803, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103851, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103899, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103901, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103973, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813103975, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104012, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104013, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104074, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104114, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104116, "dur": 132, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104253, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104298, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104301, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104451, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104453, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104509, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104516, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104607, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104644, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104723, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104725, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104864, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104971, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813104973, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105099, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105101, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105183, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105223, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105226, "dur": 329, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105565, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105572, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105627, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813105629, "dur": 588, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813106223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813106226, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813106267, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813106270, "dur": 742, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813107019, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813107022, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813107069, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813107083, "dur": 64527, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813171619, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813171623, "dur": 147, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813171780, "dur": 2840, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813174628, "dur": 6919, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813181555, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813181560, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813181609, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813181612, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813181664, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813181668, "dur": 640, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182316, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182356, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182359, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182606, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182644, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182646, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182677, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182736, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182739, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182772, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182774, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182935, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182937, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182965, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813182967, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813183125, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813183155, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813183331, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813183373, "dur": 513, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813183890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813183892, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813183922, "dur": 1368, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185294, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185296, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185339, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185342, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185382, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185384, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185416, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185505, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185507, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185538, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813185541, "dur": 543, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813186088, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813186118, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813186275, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813186304, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813186526, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813186552, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813186776, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813186815, "dur": 800, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187619, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187622, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187653, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187781, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187807, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187867, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187895, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187929, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813187931, "dur": 305, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813188240, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813188244, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813188275, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813188277, "dur": 756, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189040, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189044, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189089, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189093, "dur": 523, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189621, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189624, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189655, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189657, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189694, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189724, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189726, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189782, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189818, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813189821, "dur": 655, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813190481, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813190483, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813190522, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813190524, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813190557, "dur": 619, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191183, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191217, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191219, "dur": 251, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191473, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191475, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191504, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191508, "dur": 390, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191902, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191905, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191937, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813191939, "dur": 258, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192201, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192203, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192229, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192231, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192290, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192324, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192327, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192457, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192459, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192489, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192492, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192527, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192554, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192807, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192810, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192846, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813192849, "dur": 720, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813193573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813193577, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813193608, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813193610, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813193720, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813193748, "dur": 896, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813194648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813194651, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813194682, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813194684, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813194797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813194800, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813194838, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813194841, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195170, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195203, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195205, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195331, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195369, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195372, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195406, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195490, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195523, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195606, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195641, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813195644, "dur": 376, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813196024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813196027, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813196065, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813196067, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813196146, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813196184, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813196187, "dur": 1039, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197230, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197232, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197268, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197271, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197364, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197393, "dur": 319, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197718, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197748, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813197750, "dur": 530, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198284, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198286, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198333, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198335, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198461, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198488, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198489, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198604, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198632, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198879, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813198905, "dur": 566, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813199474, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813199477, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813199507, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813199510, "dur": 537, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200050, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200053, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200080, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200515, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200543, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200572, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200806, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200843, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813200846, "dur": 229, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201081, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201123, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201125, "dur": 463, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201593, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201595, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201632, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201634, "dur": 112, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201750, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201752, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201785, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813201787, "dur": 325, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813202117, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813202148, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813202151, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813202350, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813202379, "dur": 508, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813202891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813202893, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813202923, "dur": 722, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203652, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203696, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203698, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203733, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203768, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203770, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203809, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203812, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203855, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203857, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203893, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203896, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203932, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203935, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203970, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813203972, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204009, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204011, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204047, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204050, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204085, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204088, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204127, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204129, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204171, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204174, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204218, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204220, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204263, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204266, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204305, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204309, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204348, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204351, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204395, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204398, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204437, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204443, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204489, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204493, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204523, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204524, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204552, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204555, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204587, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204589, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204617, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204621, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204648, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204649, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204684, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204686, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204713, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204715, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204754, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204758, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204799, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204802, "dur": 28, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204832, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204835, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204875, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204878, "dur": 26, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204906, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204908, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204937, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204939, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204966, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813204968, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205005, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205007, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205064, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205067, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205102, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205105, "dur": 31, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205138, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205141, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205171, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205173, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205202, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205204, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205232, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205234, "dur": 44, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205283, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205286, "dur": 40, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205329, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205332, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205373, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205375, "dur": 303, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205683, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205685, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205725, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205727, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205761, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205763, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205800, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205803, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813205839, "dur": 186209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813392075, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813392087, "dur": 205, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813392305, "dur": 69, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813392379, "dur": 343037, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813735426, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813735429, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813735451, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813735454, "dur": 195230, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813930695, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813930698, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813930720, "dur": 780, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813931507, "dur": 12642, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813944157, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813944161, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813944202, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813944205, "dur": 1971, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813946181, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813946183, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813946200, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499813946220, "dur": 309603, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814255832, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814255835, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814255858, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814255861, "dur": 786, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814256654, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814256657, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814256693, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814256721, "dur": 734, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814257472, "dur": 16, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814257493, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814257621, "dur": 1276, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754499814258909, "dur": 17750, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 36960, "tid": 108880, "ts": 1754499814301031, "dur": 1483, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 36960, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 36960, "tid": 8589934592, "ts": 1754499812991707, "dur": 178107, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 36960, "tid": 8589934592, "ts": 1754499813169817, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 36960, "tid": 8589934592, "ts": 1754499813169823, "dur": 1672, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 36960, "tid": 108880, "ts": 1754499814302515, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 36960, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 36960, "tid": 4294967296, "ts": 1754499812891442, "dur": 1386623, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754499812924677, "dur": 9505, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754499814278148, "dur": 8201, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754499814281446, "dur": 198, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754499814287329, "dur": 31, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 36960, "tid": 108880, "ts": 1754499814302526, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754499812948136, "dur": 1735, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754499812949880, "dur": 1047, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754499812951064, "dur": 138, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754499812951202, "dur": 729, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754499812952491, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_A4F99F59C0C92426.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754499812953186, "dur": 45396, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_82714E7E5E4D2D26.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754499812999467, "dur": 3479, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754499813003452, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754499813003764, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754499813004675, "dur": 803, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754499813005967, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754499813006211, "dur": 676, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754499813007067, "dur": 240, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754499813007637, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754499813008668, "dur": 131, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_CE6A42C97D96EB0A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754499813009352, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754499813009822, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754499813010284, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754499813010624, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754499813011434, "dur": 831, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754499813014097, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754499813015096, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754499813015353, "dur": 742, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754499813016119, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754499813016379, "dur": 226, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754499813017486, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754499813023728, "dur": 152, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PackageValidationSuite.Editor.Extension.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754499813027183, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754499813027385, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754499813029921, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18308057294943403080.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754499813030710, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754499812951981, "dur": 79344, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754499813031346, "dur": 1225463, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754499814256810, "dur": 161, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754499814257163, "dur": 168, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754499814257394, "dur": 4835, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754499812952343, "dur": 79019, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813031376, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813031534, "dur": 1559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_FEB99ECBF3C51413.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754499813033700, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754499813034707, "dur": 760, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754499813035470, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813035535, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813035870, "dur": 290, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754499813036161, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754499813036218, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813036581, "dur": 869, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813037451, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754499813037573, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754499813037645, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813037804, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2772139499707579658.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754499813037977, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813038373, "dur": 1693, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@dcc61ebd6655\\Runtime\\Impulse\\CinemachineIndependentImpulseListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754499813038127, "dur": 2601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813040728, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813041821, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813042399, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813043033, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813044220, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813045248, "dur": 2217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813047465, "dur": 2807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813050273, "dur": 3745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813054018, "dur": 2933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813056952, "dur": 2826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813059779, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813061735, "dur": 3573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813065308, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813067113, "dur": 2770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813069883, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813070692, "dur": 3361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813074054, "dur": 2646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813076701, "dur": 2718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813079420, "dur": 2574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813081995, "dur": 2487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813084483, "dur": 3375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813087859, "dur": 2500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813090360, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813090533, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813091117, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754499813091863, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813092053, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813092788, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813093454, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813094424, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813094551, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813095617, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813095771, "dur": 1745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1754499813097517, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813097592, "dur": 468, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813098801, "dur": 72860, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1754499813178733, "dur": 2726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813181460, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813181538, "dur": 3711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813185250, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813185408, "dur": 3500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813188909, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813189039, "dur": 3307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813192347, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813192473, "dur": 3451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813195925, "dur": 1444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813197381, "dur": 3067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813200449, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813200819, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754499813204359, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813204681, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813205097, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754499813205276, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813205769, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754499813205838, "dur": 1050853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499812952505, "dur": 78919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813031431, "dur": 1662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B67EF2BB05ABACA5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813033264, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813033369, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_5E09CE17EB61387A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813033693, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754499813033915, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813034065, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813034387, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813034508, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754499813034821, "dur": 432, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754499813035255, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813035520, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813036186, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813036444, "dur": 1039, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813037544, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754499813037736, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5693374507049717478.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754499813037850, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813037922, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813038568, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813039434, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813040072, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813040680, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813041668, "dur": 884, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-errorhandling-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754499813041334, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813044229, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mobile.android-logcat@e05958e55753\\Editor\\Tasks\\AndroidLogcatTaskInput.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754499813042908, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813044796, "dur": 2305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813047102, "dur": 2805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813049908, "dur": 3661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813053570, "dur": 3207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813056778, "dur": 2516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813059295, "dur": 2321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813061662, "dur": 3113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813064776, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813066530, "dur": 2014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813068544, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813069965, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813071717, "dur": 4142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813075860, "dur": 2802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813078663, "dur": 2755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813081735, "dur": 811, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\EnhancedTouch\\TouchHistory.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754499813081419, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813082891, "dur": 3050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813085942, "dur": 2825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813089092, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813089275, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813089487, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813090519, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813091114, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813091490, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813091667, "dur": 2718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813094386, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813094545, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813094716, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813094939, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813094999, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813097313, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813098077, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813098169, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813098559, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813099530, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813099596, "dur": 1658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813101256, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813101756, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813101913, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813102425, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813102542, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813102714, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813103460, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813103537, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813103633, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813104014, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813104118, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813104722, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754499813104847, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813105192, "dur": 73578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813178773, "dur": 3708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813182483, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813182613, "dur": 3948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813186562, "dur": 1061, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813187634, "dur": 4080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813191715, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813191914, "dur": 3623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813195538, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813195620, "dur": 1875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813197496, "dur": 816, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813198319, "dur": 3954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813202274, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813202371, "dur": 3248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754499813205621, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754499813205726, "dur": 1050982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499812952387, "dur": 78984, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813031381, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5ECB2C4C110CA6E5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754499813033813, "dur": 1022, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5ECB2C4C110CA6E5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754499813035195, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813035283, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754499813035422, "dur": 422, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754499813036008, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813036404, "dur": 1121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754499813037552, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813037891, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813037986, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813038920, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813039597, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813040298, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813041725, "dur": 1114, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Rewrite.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754499813040929, "dur": 2048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813042977, "dur": 1944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813044922, "dur": 2137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813047060, "dur": 3059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813050120, "dur": 3189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813053310, "dur": 2681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813055992, "dur": 2188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813058180, "dur": 2188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813060369, "dur": 2673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813063042, "dur": 3327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813066370, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813067674, "dur": 2518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813070193, "dur": 2777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813072971, "dur": 3702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813076674, "dur": 2531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813079206, "dur": 2492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813081699, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813082411, "dur": 2632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813085044, "dur": 2958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813088004, "dur": 2217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813090222, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813090515, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813091116, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754499813091426, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813091488, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813092476, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813092645, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754499813092989, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813093850, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813094173, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754499813094492, "dur": 2955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813097448, "dur": 948, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813098396, "dur": 1320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813099752, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813099823, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754499813100084, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813100156, "dur": 924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813101080, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813101278, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813101758, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754499813101963, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813102402, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813102508, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813102594, "dur": 2124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813104719, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754499813104956, "dur": 73790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813178756, "dur": 3881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813182638, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813182754, "dur": 3725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813186480, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813186551, "dur": 3843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Authenticators.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813190395, "dur": 2418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813192820, "dur": 3109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813195931, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813196037, "dur": 3337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813199375, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813199488, "dur": 4055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754499813203544, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813203663, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813203736, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813203962, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813204173, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813204485, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813204551, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813204655, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754499813204929, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754499813205229, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/kcp2k.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754499813205317, "dur": 1051429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499812952317, "dur": 79036, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813031377, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813031661, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_D1C7B996A6C45BF4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813032080, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813032332, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5F1525A9150C0C6C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813032486, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813032590, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CF55AD57D7032CA8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813032705, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813032830, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_18A3D624D5A4F4D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813033042, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_DD77882189074261.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813033465, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_38DF6D39894AFB51.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813033591, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813033653, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813033779, "dur": 830, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813034610, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813034811, "dur": 517, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813035350, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813035445, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813035668, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813035740, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813036172, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813036483, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813036551, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813036635, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813036691, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813036802, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813036902, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813037023, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813037167, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813037435, "dur": 2031, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9321109326917711913.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754499813039468, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813040417, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813041167, "dur": 2109, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Grpc.Net.Client.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754499813041071, "dur": 2836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813043908, "dur": 2154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813046063, "dur": 2669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813048733, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813051146, "dur": 2479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813053626, "dur": 3860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813057486, "dur": 2700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813060187, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813062319, "dur": 3823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813066142, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813067984, "dur": 2093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813070077, "dur": 2411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813072489, "dur": 3624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813076114, "dur": 2457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813078663, "dur": 2239, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\Utilities\\AnimationPreviewUtilities.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754499813081137, "dur": 640, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\TimelineClip.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754499813081845, "dur": 1076, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\TimelineAsset_CreateRemove.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754499813078571, "dur": 6596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813085167, "dur": 2872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813088083, "dur": 2155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813090239, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813090524, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813091124, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813091557, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813091616, "dur": 1909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813093526, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813093719, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813094196, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813094585, "dur": 1238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813095824, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813096106, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813096171, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813097061, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813097177, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813097943, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813098095, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813098924, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813099195, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813100280, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813101017, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813101760, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813101926, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813102593, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813102685, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813102871, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813103417, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813103481, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813103594, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813103671, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754499813103872, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813104337, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813104472, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813104738, "dur": 74040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813178780, "dur": 5201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813183982, "dur": 1313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813185305, "dur": 4061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813189368, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813189632, "dur": 3860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813193493, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813193584, "dur": 3511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813197097, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813197240, "dur": 3692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813200933, "dur": 824, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813201766, "dur": 3392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813205158, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813205224, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754499813205312, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754499813205377, "dur": 1051335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499812952756, "dur": 78838, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813031599, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A9644AD1997CC33F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813032091, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813032451, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_22A35CACB552B292.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813032852, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_D2083F65E1F978AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813033071, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_AE2A3D5B7498DE25.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813033803, "dur": 1065, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_AE2A3D5B7498DE25.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813034870, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754499813035302, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754499813035459, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813035644, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754499813035900, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754499813036027, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813036082, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813036393, "dur": 1079, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754499813037541, "dur": 2521, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9124628235568457678.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754499813040332, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mirror\\Hosting\\Edgegap\\Models\\SDK\\Paginator.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754499813040063, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813040941, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813041956, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813043006, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813044238, "dur": 3234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813047472, "dur": 2381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813049854, "dur": 2811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813052665, "dur": 2265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813054930, "dur": 2679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813057610, "dur": 3090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813060701, "dur": 2422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813063124, "dur": 2616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813065740, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813067308, "dur": 2002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813069310, "dur": 1863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813071173, "dur": 3224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813074397, "dur": 3165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813077563, "dur": 2471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813081271, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.playmode@7f4b34d911b7\\Common\\Editor\\PackagesSync.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754499813080034, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813082357, "dur": 784, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Actions\\Interactions\\MultiTapInteraction.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754499813082183, "dur": 3530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813085714, "dur": 2483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813088956, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@388540dd9ce6\\UnityEditor.TestRunner\\TestRunner\\Utils\\ITestListCache.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754499813088198, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813089521, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813090511, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813091118, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813091445, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813092028, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813093069, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813093321, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813093599, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813094551, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813094755, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813095049, "dur": 1753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813096802, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813096969, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813097289, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813097515, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813097587, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813098400, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813098610, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813098772, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813099035, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813099108, "dur": 1352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813100461, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813101030, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813101247, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813101716, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813101874, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813102558, "dur": 2162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813104722, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754499813104904, "dur": 73836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813178765, "dur": 4083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/kcp2k.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813182849, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813183148, "dur": 4039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813187188, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813187807, "dur": 4299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813192108, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813192219, "dur": 3057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813195276, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813195388, "dur": 3180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813198569, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813198629, "dur": 3865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754499813202495, "dur": 1549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813204470, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813204629, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813205219, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754499813205324, "dur": 1051408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499812952708, "dur": 78727, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813031439, "dur": 2381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CBC203599E566618.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813034888, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754499813035082, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/kcp2k.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754499813035175, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813035308, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813035645, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754499813035796, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813036523, "dur": 14249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813050773, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813051228, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813051458, "dur": 3075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813054534, "dur": 3057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813057591, "dur": 2601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813060192, "dur": 2521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813062713, "dur": 3165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813065878, "dur": 2152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813068030, "dur": 2428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813070458, "dur": 3824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813074283, "dur": 3300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813077584, "dur": 2569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813080154, "dur": 1757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813081912, "dur": 2397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813084310, "dur": 3656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813087967, "dur": 2134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813090102, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813090527, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813091170, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813091589, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813091787, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813092739, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813093150, "dur": 995, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813094151, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813094458, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813095278, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813095579, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813095939, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813096018, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813097216, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813097322, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813097626, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813098296, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813098776, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813099048, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813100265, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813100599, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813100827, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813101330, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813101424, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813101773, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813102548, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813102722, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754499813102947, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813103454, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813103610, "dur": 1129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813104740, "dur": 74012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813178753, "dur": 3448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813182203, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813182319, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleWebTransport.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813184418, "dur": 1683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813186112, "dur": 3431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813189544, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813189717, "dur": 3451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813193170, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813193739, "dur": 3546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813197286, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813197731, "dur": 3752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813201490, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813201621, "dur": 3079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754499813204701, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813204859, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813205036, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754499813205227, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754499813205303, "dur": 1051404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499812952474, "dur": 78908, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813031391, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_08B2EE5ACE61D9A1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813032223, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_785B75CD6E98288C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813032855, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_C4E65B30254244B8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813033065, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_FC9FF88BB6DDE854.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813033805, "dur": 1237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_FC9FF88BB6DDE854.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813035184, "dur": 647, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754499813035855, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813036169, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813036415, "dur": 1260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754499813037873, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813038194, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813038933, "dur": 982, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mirror\\Examples\\HexSpatialHash\\Scripts\\Hex2DNetworkManager.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754499813038715, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813040027, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813040757, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813042050, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813043024, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813044666, "dur": 556, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\VFXGraph\\VFXURPLitPlanarPrimitiveOutput.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754499813044162, "dur": 3327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813047489, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813050076, "dur": 3654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813053731, "dur": 2844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813056575, "dur": 2800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813059375, "dur": 2806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813062182, "dur": 3410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813065593, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813067738, "dur": 2862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813070601, "dur": 3316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813073917, "dur": 2903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813076820, "dur": 2799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813079619, "dur": 1838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813081731, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Events\\DeltaStateEvent.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754499813081458, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813082763, "dur": 3637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813086401, "dur": 3324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813089726, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813090556, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813091127, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813091584, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813091705, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813091760, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813092716, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813092867, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813093086, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813093192, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813094052, "dur": 1335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813095403, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813095460, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813095716, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813095777, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813096181, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813097060, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813097189, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813098105, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813098243, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813098949, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813099120, "dur": 1172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813100293, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813100495, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813101035, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754499813101205, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813101274, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813102649, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813102760, "dur": 1997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813104757, "dur": 73970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813178729, "dur": 2840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813181570, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813181662, "dur": 3675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813185338, "dur": 2909, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813188256, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813191725, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813191919, "dur": 3342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813195262, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813195357, "dur": 4203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813199561, "dur": 1003, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813200572, "dur": 3509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754499813204082, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813204482, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754499813205157, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754499813205284, "dur": 1051502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499812952916, "dur": 78713, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813031635, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_A8EE771E29308445.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754499813032120, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813032721, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813033043, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_53F7BBBA74CCF99D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754499813033704, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754499813033980, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754499813034568, "dur": 1622, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754499813036215, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813037184, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813037428, "dur": 2853, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754499813040282, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813040634, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813041651, "dur": 859, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HostFiltering.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754499813040990, "dur": 1626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813042616, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813043191, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813043837, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813045246, "dur": 2896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813048142, "dur": 3221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813051364, "dur": 3089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813054454, "dur": 3609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813058065, "dur": 2698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813060763, "dur": 3046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813063809, "dur": 2839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813066649, "dur": 2470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813069119, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813070606, "dur": 2988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813073595, "dur": 2664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813078507, "dur": 2159, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsReflectionUtility.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754499813076259, "dur": 5013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813081272, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813082268, "dur": 2042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813084311, "dur": 3224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813088683, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetMenu\\AssetMenuOperations.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754499813087535, "dur": 2927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813090463, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813090530, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813091373, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754499813092147, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813093775, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813093881, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754499813094067, "dur": 1648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813095716, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813095830, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754499813096483, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813097438, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813097912, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813098506, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813098661, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754499813098848, "dur": 1897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813100746, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813100954, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813101771, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813102560, "dur": 2169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813104729, "dur": 74008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813178745, "dur": 2703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813181449, "dur": 1234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813182691, "dur": 2794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Telepathy.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813185486, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813185551, "dur": 3660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813189213, "dur": 5584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813194812, "dur": 3574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813198387, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813198480, "dur": 2033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813200513, "dur": 1078, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813201602, "dur": 3388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754499813204991, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754499813205102, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.VirtualProjects.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754499813205280, "dur": 1051508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499812953074, "dur": 78662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813031741, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_18C55C75F47E4EB6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813032854, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_8AA5619BE74CBCE9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813033039, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7CCD9826AD528688.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813033702, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754499813033902, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813035032, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813035185, "dur": 480, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754499813035761, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813036009, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754499813036195, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813036601, "dur": 854, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813037455, "dur": 2756, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754499813040212, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813040565, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813040900, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813041651, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813042155, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813043328, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813044662, "dur": 2606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813047268, "dur": 2679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813049948, "dur": 3516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813053464, "dur": 3196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813056661, "dur": 2414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813059075, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813060961, "dur": 3041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813064003, "dur": 2842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813066846, "dur": 2655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813069502, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813070831, "dur": 3049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813073881, "dur": 2619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813076500, "dur": 2584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813081072, "dur": 770, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed\\Runtime\\Attributes\\PerformanceAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754499813079085, "dur": 2821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813082010, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\BuildPipeline\\LinkFileGenerator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754499813081906, "dur": 2985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813084892, "dur": 3198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813088696, "dur": 775, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@388540dd9ce6\\UnityEditor.TestRunner\\UnityTestProtocol\\IUtpMessageReporter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754499813088091, "dur": 2194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813090286, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813090520, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813091107, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813091316, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813091391, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813092430, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813092611, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813092785, "dur": 1229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813094015, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813094188, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813094552, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813095003, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813095073, "dur": 1642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813096716, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813096956, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813097163, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813097537, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813097779, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813099084, "dur": 722, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813099840, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754499813100076, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813100763, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813100945, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813101772, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813102560, "dur": 2162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813104723, "dur": 66783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813175621, "dur": 406, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 9, "ts": 1754499813176028, "dur": 1108, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 9, "ts": 1754499813177136, "dur": 74, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 9, "ts": 1754499813171507, "dur": 5707, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813177215, "dur": 4426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813181642, "dur": 2886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813184533, "dur": 997, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813185538, "dur": 4543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813190082, "dur": 2218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813192310, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813194619, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813194687, "dur": 3126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Edgegap.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813197814, "dur": 2709, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813200532, "dur": 3090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754499813203623, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813203835, "dur": 885, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754499813205068, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Notifications.Android.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1754499813205295, "dur": 1051478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499812952814, "dur": 78792, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813031610, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_28478B3677CA5D72.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754499813032155, "dur": 811, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813033024, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813034577, "dur": 870, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754499813035641, "dur": 483, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754499813036174, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813036440, "dur": 998, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813037438, "dur": 2631, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754499813040071, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813040562, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Text.Encodings.Web.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754499813040477, "dur": 1568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813042046, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813042915, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813044135, "dur": 3014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813047150, "dur": 2081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813049231, "dur": 3333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813052565, "dur": 3095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813055661, "dur": 2498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813058160, "dur": 2350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813060511, "dur": 3222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813063733, "dur": 2715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813066448, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813068360, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813069904, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813071252, "dur": 3107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813074360, "dur": 2720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813077081, "dur": 2353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813081222, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.package-validation-suite@536239bd7458\\Editor\\ValidationSuite\\ValidationTests\\ApiValidation.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754499813079434, "dur": 2375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813082121, "dur": 929, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\Internal\\InputStateWindow.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754499813081809, "dur": 3922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813088731, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Tool\\FindTool.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754499813085731, "dur": 3601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813089482, "dur": 1030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813090512, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813091371, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754499813092232, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813093333, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813093715, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754499813093920, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813094170, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754499813094367, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754499813094589, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813095021, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754499813095208, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813095298, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813096457, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813096753, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813096870, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754499813097141, "dur": 1889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813099030, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813099098, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Playmode.ref.dll_25A6388924073FB9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754499813099178, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813099425, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813099504, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813100318, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813100499, "dur": 1263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813101763, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813102569, "dur": 2155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813104724, "dur": 72506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813177232, "dur": 1503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813178736, "dur": 2743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813181480, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813181554, "dur": 4554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813186109, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813186301, "dur": 4787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813191089, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813191193, "dur": 6553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813197747, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813198298, "dur": 3735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813202035, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813202140, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754499813204839, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754499813205332, "dur": 1051369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499812953002, "dur": 78706, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813031711, "dur": 1421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_090E8A4E264B09AD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813033133, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813033335, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_A4F99F59C0C92426.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813033747, "dur": 330, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_A4F99F59C0C92426.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813034612, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813034745, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813034862, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813034992, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813035094, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754499813035639, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754499813035806, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813036110, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813036471, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813037045, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754499813037109, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813037421, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754499813037636, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754499813037886, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813038433, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813039170, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813040504, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813042003, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813042647, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813044238, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Utils\\EditModeGUIUtils.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754499813043824, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813045141, "dur": 2329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813047470, "dur": 2689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813050160, "dur": 3440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813053601, "dur": 3674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813057276, "dur": 2254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813059530, "dur": 2757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813062288, "dur": 3930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813066218, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813067780, "dur": 2963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813070743, "dur": 3676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813074420, "dur": 2913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813077334, "dur": 2492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813079827, "dur": 2182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813082010, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813083852, "dur": 3017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813088678, "dur": 772, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Developer\\UpdateReport\\UpdateReportDialog.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754499813086869, "dur": 3369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813090239, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813090518, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813091120, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813091452, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813092397, "dur": 800, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813093256, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813093558, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813093850, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813094048, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813095433, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813095491, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813096084, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813097121, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813097316, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813097620, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813100142, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813100306, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813100497, "dur": 1309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813101806, "dur": 766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813102572, "dur": 2143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813104717, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754499813104879, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813104945, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813105396, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813105502, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813106093, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813106213, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813107873, "dur": 284133, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813398230, "dur": 333359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754499813398216, "dur": 335851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813735144, "dur": 205, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754499813735808, "dur": 194868, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754499813944036, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754499813944021, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754499813944165, "dur": 2035, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754499813946205, "dur": 310500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499812953180, "dur": 78611, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813031794, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_34C7DB8E4ECB1B7B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813032190, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813032538, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813032882, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_39C6E023018929F3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813033054, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_3EC188A9A2685E71.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813033753, "dur": 2030, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_3EC188A9A2685E71.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813035795, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813036472, "dur": 24271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813060744, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813061131, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813061316, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813061405, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813061616, "dur": 28749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813090367, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813090578, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813090703, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813091109, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813091224, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813091719, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813091880, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813092100, "dur": 977, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813093082, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813094281, "dur": 1031, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813095320, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813096150, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813096560, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813097302, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813097458, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813097931, "dur": 1814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813099745, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813100097, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813100322, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813100461, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813100642, "dur": 1152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813101794, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813102557, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754499813102713, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813102783, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813103111, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813103207, "dur": 1525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813104732, "dur": 74001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813178735, "dur": 5082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813183819, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813183908, "dur": 3723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813187632, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813188273, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813190398, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813190492, "dur": 3408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813193901, "dur": 1274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813195183, "dur": 8983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754499813204167, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813204312, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813204830, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813205041, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Mirror.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754499813205132, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813205218, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754499813205275, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813205703, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754499813205771, "dur": 1050905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499812953151, "dur": 78598, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813031752, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_8F31C36E8B05B73D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813032139, "dur": 751, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813033076, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_7C8AF205DD25174E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813033808, "dur": 1246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_7C8AF205DD25174E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813035099, "dur": 565, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754499813035796, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813036545, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813037392, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754499813037497, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754499813037878, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813038167, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813038534, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813038860, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813039379, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813040159, "dur": 940, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mirror\\Transports\\SimpleWeb\\SimpleWeb\\Common\\Utils.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754499813039805, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813041166, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813042326, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813044679, "dur": 669, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Recording\\TimelineRecording.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754499813043479, "dur": 2052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813045532, "dur": 3024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813048557, "dur": 2993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813051551, "dur": 3076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813054628, "dur": 4164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813058793, "dur": 2057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813060851, "dur": 3039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813063891, "dur": 2647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813066539, "dur": 2095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813068635, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813069489, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813070532, "dur": 3076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813073609, "dur": 3053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813076663, "dur": 2290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813078953, "dur": 2466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813081420, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813082099, "dur": 739, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\EditorInputControlLayoutCache.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754499813081897, "dur": 2755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813084653, "dur": 3198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813087852, "dur": 2411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813090264, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813090514, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813091370, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813091801, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813092556, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813092825, "dur": 897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813093723, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813093866, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813095230, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813095528, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813095695, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813095966, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813096267, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813096329, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813096971, "dur": 687, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813097662, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813098203, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813098298, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813098467, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813098662, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813098974, "dur": 1695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813100670, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813100773, "dur": 1016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813101790, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813102549, "dur": 1514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813104065, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754499813104261, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813104624, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813104726, "dur": 74004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813178732, "dur": 3512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813182244, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813182614, "dur": 2697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813185312, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813185526, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813187857, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813187932, "dur": 3456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813191389, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813191492, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813193843, "dur": 1654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813195503, "dur": 6079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/EncryptionTransportEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754499813201584, "dur": 2798, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813204618, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813204762, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754499813205008, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.022.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754499813205128, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754499813205296, "dur": 1051470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499812953204, "dur": 78634, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813031842, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_82AFBD6736063513.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813032111, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813032237, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_2A749ACEEBE3A48E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813032886, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_2A749ACEEBE3A48E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813033050, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_10364B2612418FBF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813033751, "dur": 358, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_10364B2612418FBF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813034191, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754499813034253, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813034437, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754499813034584, "dur": 1016, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754499813035602, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813035691, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813036153, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813036402, "dur": 1271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754499813037899, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813038210, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813038623, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813038958, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813039379, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813040029, "dur": 979, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\DocCodeSamples.Tests\\GamepadExample.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754499813039684, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813041810, "dur": 1355, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754499813041069, "dur": 2283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813043352, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813044236, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Attributes\\ActiveInModeAttribute.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754499813044025, "dur": 1906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813045931, "dur": 3110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813049041, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813051405, "dur": 3239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813054645, "dur": 3153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813057798, "dur": 2509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813060308, "dur": 2842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813063151, "dur": 3213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813066364, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813068294, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813070405, "dur": 3176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813073581, "dur": 3371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813076953, "dur": 2773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813081090, "dur": 687, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.package-validation-suite@536239bd7458\\Editor\\ValidationSuite\\Public\\TestOutputType.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754499813079727, "dur": 2404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813082131, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813083960, "dur": 3440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813088823, "dur": 879, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetsUtils\\LoadAsset.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754499813087401, "dur": 3011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813090413, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813090516, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813091350, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813091602, "dur": 1767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813093370, "dur": 1236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813094662, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813094851, "dur": 831, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813095688, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813096534, "dur": 2180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813098758, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813098962, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813099019, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813099565, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813099862, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754499813100030, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813100839, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813100930, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813101782, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813102551, "dur": 2176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813104727, "dur": 74047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813178779, "dur": 3772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813182552, "dur": 2815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813185373, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813187808, "dur": 1988, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813189807, "dur": 2421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.022.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813192229, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813192547, "dur": 2573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813195125, "dur": 1031, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813196164, "dur": 2365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813198529, "dur": 2550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813201093, "dur": 3832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754499813204925, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813205135, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813205278, "dur": 738773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754499813944116, "dur": 311674, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754499813944058, "dur": 311734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754499814255809, "dur": 813, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754499812953327, "dur": 78566, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813031894, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_915E418E1D60FCA1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813032245, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_DDA8E091E3967B34.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813032857, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E1660E22076E251.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813033061, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_BC191A0A0D36605B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813033752, "dur": 400, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_BC191A0A0D36605B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813034155, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813035030, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813035407, "dur": 427, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754499813036006, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754499813036206, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813036698, "dur": 3886, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813040589, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813040925, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813041663, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813042078, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813042548, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813043285, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813044204, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\AnimationPlayableAssetInspector.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754499813043964, "dur": 2049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813046014, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813048126, "dur": 2948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813051074, "dur": 2978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813054053, "dur": 2809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813056863, "dur": 2305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813059169, "dur": 3247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813062417, "dur": 3410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813065828, "dur": 2278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813068106, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813069765, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813071620, "dur": 3550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813075171, "dur": 2642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813077814, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813080243, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813080668, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813081177, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813081742, "dur": 2120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813083863, "dur": 3075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813088759, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Configuration\\CloudEdition\\Welcome\\CloudEditionWelcomeWindow.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754499813086938, "dur": 3382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813090321, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813090525, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813091112, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813091595, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813091658, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813091975, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813092169, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813092761, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813092881, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813093252, "dur": 1233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813094486, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813094568, "dur": 1171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813095740, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813095849, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813096826, "dur": 826, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813097659, "dur": 1355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813099015, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813099384, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813099838, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813100313, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813100810, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813100880, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813101783, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813102543, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813102699, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813103319, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813103411, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813103696, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754499813103792, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813104103, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813104748, "dur": 74013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813178779, "dur": 3848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Transports.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813182628, "dur": 711, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813183340, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Transports.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813183397, "dur": 3249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813186647, "dur": 1236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813187892, "dur": 2562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813190455, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813190541, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.CompilerSymbols.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813192809, "dur": 1841, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813194660, "dur": 2669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813197330, "dur": 1568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813198905, "dur": 3101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813202008, "dur": 884, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754499813202903, "dur": 2802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754499813205788, "dur": 1050886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499812953235, "dur": 78610, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813031867, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_48A48871E8231606.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813032184, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813032264, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A5F21C0EBB6C174C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813032517, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_674D8A39E28088FE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813032605, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813032821, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B72D600307EFC5F0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813032961, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_196DDF0992B5C827.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813033732, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813033787, "dur": 896, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_196DDF0992B5C827.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813034687, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754499813034793, "dur": 528, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754499813035420, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754499813035640, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754499813035744, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813035851, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754499813036031, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813036123, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813036330, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813036803, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813036985, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813037396, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754499813037518, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813037709, "dur": 1473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813039183, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6886376194011666012.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754499813039323, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813041900, "dur": 1293, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Threading.Tasks.Dataflow.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754499813040459, "dur": 2988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813044081, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\State\\PlayRange.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754499813043448, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813044732, "dur": 2076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813046808, "dur": 3095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813049904, "dur": 2825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813052730, "dur": 2897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813055628, "dur": 3433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813059062, "dur": 3072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813062135, "dur": 2685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813064821, "dur": 3003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813067825, "dur": 2556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813070382, "dur": 2774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813073157, "dur": 3417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813076575, "dur": 2284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813078860, "dur": 2341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813081202, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813082519, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Devices\\IEventMerger.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754499813081937, "dur": 2929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813084867, "dur": 2765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813087633, "dur": 2556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813090190, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813090544, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813091377, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813091589, "dur": 632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813092229, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813093061, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813093179, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813093593, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813094768, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813095190, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813095483, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813095791, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813096218, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813096286, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813097564, "dur": 863, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813098427, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813098481, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813098720, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813099022, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813099701, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813099852, "dur": 711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754499813100602, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813101122, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813101225, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813101761, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813102551, "dur": 2177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813104728, "dur": 74023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813178752, "dur": 3852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813182606, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813182955, "dur": 3731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813186687, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813186840, "dur": 4048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813190890, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813191188, "dur": 3934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813195123, "dur": 2104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813197236, "dur": 2697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813199934, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813200069, "dur": 3374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754499813203443, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813203923, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813204034, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813204487, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813204603, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813204783, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813204870, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754499813205071, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.Common.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754499813205288, "dur": 1051508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754499814270791, "dur": 4978, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 36960, "tid": 108880, "ts": 1754499814303155, "dur": 53516, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 36960, "tid": 108880, "ts": 1754499814356750, "dur": 2362, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 36960, "tid": 108880, "ts": 1754499814297867, "dur": 62075, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}